import axios from "@/core/axios";
import { Claim } from "@/hooks/useClaim";

export class ClaimService {
  private baseUrl = "v1/claims";

  async getDocuments() {
    const res = await axios.get(`${this.baseUrl}/documents/required`);

    return res.data as Claim[];
  }

  async remainingCoverage(
    id: string
  ): Promise<{ idPoliza: number; montoAsegurado: number }> {
    const res = await axios.post(`${this.baseUrl}/remaining-coverage`, {
      idPoliza: id,
    });

    return res.data;
  }
}
