"use client";
import React, { useState, useCallback, useEffect } from "react";
import * as S from "./MyReports.styles";
import { StatusTag, FilterCheckbox, Table, StatusText } from "@/app/components/common";
import { Report, ReportStatus, StatusType } from "@/types";
import { useRouter } from "next/navigation";
import { useReports } from "@/hooks/useReports";

const MyReports = () => {
  const router = useRouter();
  const [showIncomplete, setShowIncomplete] = useState(false);
  const [showFinished, setShowFinished] = useState(false);

  // Hook para manejar reportes desde la API
  const {
    reports,
    loading,
    error,
    fetchReports
  } = useReports();

  // Función para mapear el estado de reclamación al formato esperado
  const mapReportStatus = (status: string): ReportStatus => {
    switch (status.toLowerCase()) {
      case 'propuesta finiquito':
        return 'Propuesta finiquito';
      case 'finiquito':
        return 'Finiquito';
      case 'documentos':
        return 'Documentos';
      case 'proceso de pago':
        return 'Proceso de pago';
      case 'pago':
      case 'procesando pago':
        return 'Pago';
      case 'finalizado':
      case 'completado':
        return 'Finalizado';
      default:
        return 'Documentos'; // Default fallback
    }
  };

  // Función para mapear el estatus de póliza
  const mapPolizaStatus = (status: string): StatusType => {
    switch (status.toLowerCase()) {
      case 'cancelada':
        return 'cancelada';
      case 'vigente':
        return 'Vigente';
      case 'vencida':
        return 'Vencida';
      default:
        return 'Vigente'; // Default fallback
    }
  };

  // Mapear datos de la API al formato esperado por la tabla
  const allReports: Report[] = reports.map(report => ({
    poliza: report.nombreProducto,
    numeroPoliza: report.numeroPoliza,
    estatusPoliza: mapPolizaStatus(report.estatusPoliza),
    periodo: `${report.vigencia.fechaInicio} al ${report.vigencia.fechaFin}`,
    sumaAsegurada: `$${report.planSeleccionado.montoAsegurado}`,
    remanenteSuma: `$${(parseFloat(report.planSeleccionado.montoAsegurado.replace(/,/g, '')) - parseFloat(report.totalReclamado.replace(/,/g, ''))).toLocaleString()}`,
    estatusReporte: mapReportStatus(report.estatusReclamacion)
  }));

  // Cargar reportes al montar el componente
  useEffect(() => {
    fetchReports({
      page: 1,
      limit: 50,
      sortBy: 'createdAt',
      sortDir: 'desc'
    });
  }, [fetchReports]);

  const handleStatusClick = useCallback((numeroPoliza: string) => {
    router.push(`/accident-reports/${numeroPoliza}`);
  }, [router]);

  const handleNewReport = useCallback(() => {
    router.push("/siniestros/select");
  }, [router]);

  const filteredReports = allReports.filter(report => {
    if (!showIncomplete && !showFinished) return true;
    if (showIncomplete && ["Documentos", "Finiquito"].includes(report.estatusReporte)) return true;
    if (showFinished && ["Finalizado", "Pago"].includes(report.estatusReporte)) return true;
    return false;
  });

  const sortedReports = [...filteredReports].reverse();

  // Mostrar loading state
  if (loading) {
    return (
      <S.Container>
        <S.TitleContainer>
          <S.Title>Mis reportes</S.Title>
          <S.HelpButton href="/faq" target="_blank" rel="noopener noreferrer">Ayuda</S.HelpButton>
        </S.TitleContainer>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          Cargando reportes...
        </div>
      </S.Container>
    );
  }

  // Mostrar error state
  if (error) {
    return (
      <S.Container>
        <S.TitleContainer>
          <S.Title>Mis reportes</S.Title>
          <S.HelpButton href="/faq" target="_blank" rel="noopener noreferrer">Ayuda</S.HelpButton>
        </S.TitleContainer>
        <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
          Error al cargar reportes: {error}
        </div>
      </S.Container>
    );
  }

  return (
    <S.Container>
      <S.TitleContainer>
        <S.Title>Mis reportes</S.Title>
        <S.HelpButton href="/faq" target="_blank" rel="noopener noreferrer">Ayuda</S.HelpButton>
      </S.TitleContainer>

      <S.FilterSection>
        <S.ReportButton onClick={handleNewReport}>
          Reportar un siniestro
        </S.ReportButton>
        <S.CheckboxContainer>
          <FilterCheckbox
            id="incomplete"
            label="Reportes incompletos"
            checked={showIncomplete}
            onChange={setShowIncomplete}
          />
          <FilterCheckbox
            id="finished"
            label="Reportes finalizados"
            checked={showFinished}
            onChange={setShowFinished}
          />
        </S.CheckboxContainer>
      </S.FilterSection>

      <Table
        data={sortedReports}
        columns={[
          { header: 'Póliza', accessor: 'poliza' },
          { header: 'Número de póliza', accessor: 'numeroPoliza' },
          {
            header: 'Estatus de la póliza',
            accessor: 'estatusPoliza',
            render: (value) => (
              <StatusText status={value as StatusType}>
                {value}
              </StatusText>
            )
          },
          { header: 'Periodo', accessor: 'periodo' },
          { header: 'Suma asegurada', accessor: 'sumaAsegurada' },
          { header: 'Remanente de suma asegurada', accessor: 'remanenteSuma' },
          {
            header: 'Estatus del reporte',
            accessor: 'estatusReporte',
            render: (value, item) => (
              <StatusTag
                status={value as ReportStatus}
                onClick={() => handleStatusClick(item.numeroPoliza)}
              />
            )
          }
        ]}
        emptyMessage="No se encontraron reportes disponibles."
        aria-label="Mis reportes"
      />
    </S.Container>
  );
};

export default MyReports;